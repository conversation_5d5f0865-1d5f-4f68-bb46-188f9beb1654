"""
Pydantic models for the PYQs Admin module.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class Level(BaseModel):
    """
    Pydantic model for a level.

    Attributes:
        id (int): The ID of the level
        name (str): The name of the level
    """
    id: int
    name: str


class Syllabus(BaseModel):
    """
    Pydantic model for a syllabus.

    Attributes:
        id (int): The ID of the syllabus
        syllabus (str): The name of the syllabus
    """
    id: int
    syllabus: str


class Grade(BaseModel):
    """
    Pydantic model for a grade.

    Attributes:
        id (int): The ID of the grade
        grade (str): The name of the grade
    """
    id: int
    grade: str


class Subject(BaseModel):
    """
    Pydantic model for a subject.

    Attributes:
        id (int): The ID of the subject
        subject (str): The name of the subject
    """
    id: int
    subject: str


class ExamCreate(BaseModel):
    """
    Pydantic model for creating a new exam.

    Attributes:
        exam_name (str): The name of the exam
        level (str): The level of the exam
        syllabus (str): The syllabus of the exam
        grade (str): The grade of the exam
        subject (str): The subject of the exam
        syllabus_text (Optional[str]): The syllabus text of the exam
    """
    exam_name: str
    level: str
    syllabus: str
    grade: str
    subject: str
    syllabus_text: Optional[str] = None


class ExamUpdate(BaseModel):
    """
    Pydantic model for updating an existing exam.

    Attributes:
        exam_name (str): The name of the exam
        level (str): The level of the exam
        syllabus (str): The syllabus of the exam
        grade (str): The grade of the exam
        subject (str): The subject of the exam
        syllabus_text (Optional[str]): The syllabus text of the exam
    """
    exam_name: str
    level: str
    syllabus: str
    grade: str
    subject: str
    syllabus_text: Optional[str] = None


class ExamResponse(BaseModel):
    """
    Pydantic model for an exam response.

    Attributes:
        id (int): The ID of the exam
        exam_name (str): The name of the exam
        level (str): The level of the exam
        syllabus (str): The syllabus of the exam
        grade (str): The grade of the exam
        subject (str): The subject of the exam
        syllabus_text (Optional[str]): The syllabus text of the exam
    """
    id: int
    exam_name: str
    level: str
    syllabus: str
    grade: str
    subject: str
    syllabus_text: Optional[str] = None


class ExamListResponse(BaseModel):
    """
    Pydantic model for a paginated list of exams.

    Attributes:
        exams (List[ExamResponse]): The list of exams
        total (int): The total number of exams
        page (int): The current page
        limit (int): The number of exams per page
        pages (int): The total number of pages
    """
    exams: List[ExamResponse]
    total: int
    page: int
    limit: int
    pages: int


class ExamDocumentCreate(BaseModel):
    """
    Pydantic model for creating a new exam document.

    Attributes:
        year (int): The year of the exam
        month (Optional[str]): The month of the exam
        shift (Optional[str]): The shift of the exam
        question_paper_path (str): The path to the question paper in S3
    """
    year: int
    month: Optional[str] = None
    shift: Optional[str] = None
    question_paper_path: str


class ExamDocumentResponse(BaseModel):
    """
    Pydantic model for an exam document response.

    Attributes:
        id (int): The ID of the document
        exam_id (int): The ID of the exam
        year (int): The year of the exam
        month (Optional[str]): The month of the exam
        shift (Optional[str]): The shift of the exam
        question_paper_path (str): The path to the question paper in S3
        has_extracted_content (bool): Whether the document has extracted content
        date_created (datetime): The date the document was created
        created_by (str): The username of the creator
    """
    id: int
    exam_id: int
    year: int
    month: Optional[str] = None
    shift: Optional[str] = None
    question_paper_path: str
    has_extracted_content: bool
    date_created: datetime
    created_by: str


class ExamDocumentListResponse(BaseModel):
    """
    Pydantic model for a paginated list of exam documents.

    Attributes:
        documents (List[ExamDocumentResponse]): The list of documents
        total (int): The total number of documents
        page (int): The current page
        limit (int): The number of documents per page
        pages (int): The total number of pages
        exam (ExamResponse): The exam the documents belong to
    """
    documents: List[ExamDocumentResponse]
    total: int
    page: int
    limit: int
    pages: int
    exam: ExamResponse


class ExamDocumentDetailResponse(BaseModel):
    """
    Pydantic model for a detailed exam document response.

    Attributes:
        id (int): The ID of the document
        exam_id (int): The ID of the exam
        year (int): The year of the exam
        month (Optional[str]): The month of the exam
        shift (Optional[str]): The shift of the exam
        question_paper_path (str): The path to the question paper in S3
        extracted_content (Optional[str]): The extracted HTML content
        date_created (datetime): The date the document was created
        created_by (str): The username of the creator
        exam (ExamResponse): The exam the document belongs to
    """
    id: int
    exam_id: int
    year: int
    month: Optional[str] = None
    shift: Optional[str] = None
    question_paper_path: str
    extracted_content: Optional[str] = None
    date_created: datetime
    created_by: str
    exam: ExamResponse


class ExamSolutionCreate(BaseModel):
    """
    Pydantic model for creating a new exam solution.

    Attributes:
        exam_dtl_id (int): The ID of the exam document
        question (str): The question text
        question_type (Optional[str]): The question type (e.g., MCQ, descriptive)
        option1 (Optional[str]): Option 1 for MCQs
        option2 (Optional[str]): Option 2 for MCQs
        option3 (Optional[str]): Option 3 for MCQs
        option4 (Optional[str]): Option 4 for MCQs
        option5 (Optional[str]): Option 5 for MCQs
        answer (Optional[str]): The correct answer
        marks (Optional[float]): The marks for the question
        negative_mark (Optional[float]): The negative marks for the question
        topic (Optional[str]): The topic of the question
        subtopic (Optional[str]): The subtopic of the question
    """
    exam_dtl_id: int
    question: str
    question_type: Optional[str] = None
    option1: Optional[str] = None
    option2: Optional[str] = None
    option3: Optional[str] = None
    option4: Optional[str] = None
    option5: Optional[str] = None
    answer: Optional[str] = None
    marks: Optional[float] = None
    negative_mark: Optional[float] = None
    topic: Optional[str] = None
    subtopic: Optional[str] = None


class ExamSolutionUpdate(BaseModel):
    """
    Pydantic model for updating an existing exam solution.

    Attributes:
        question (str): The question text
        question_type (Optional[str]): The question type (e.g., MCQ, descriptive)
        option1 (Optional[str]): Option 1 for MCQs
        option2 (Optional[str]): Option 2 for MCQs
        option3 (Optional[str]): Option 3 for MCQs
        option4 (Optional[str]): Option 4 for MCQs
        option5 (Optional[str]): Option 5 for MCQs
        answer (Optional[str]): The correct answer
        marks (Optional[float]): The marks for the question
        negative_mark (Optional[float]): The negative marks for the question
        topic (Optional[str]): The topic of the question
        subtopic (Optional[str]): The subtopic of the question
    """
    question: str
    question_type: Optional[str] = None
    option1: Optional[str] = None
    option2: Optional[str] = None
    option3: Optional[str] = None
    option4: Optional[str] = None
    option5: Optional[str] = None
    answer: Optional[str] = None
    marks: Optional[float] = None
    negative_mark: Optional[float] = None
    topic: Optional[str] = None
    subtopic: Optional[str] = None


class ExamSolutionResponse(BaseModel):
    """
    Pydantic model for an exam solution response.

    Attributes:
        id (int): The ID of the solution
        exam_dtl_id (int): The ID of the exam document
        question (str): The question text
        question_type (Optional[str]): The question type (e.g., MCQ, descriptive)
        option1 (Optional[str]): Option 1 for MCQs
        option2 (Optional[str]): Option 2 for MCQs
        option3 (Optional[str]): Option 3 for MCQs
        option4 (Optional[str]): Option 4 for MCQs
        option5 (Optional[str]): Option 5 for MCQs
        answer (Optional[str]): The correct answer
        marks (Optional[float]): The marks for the question
        negative_mark (Optional[float]): The negative marks for the question
        topic (Optional[str]): The topic of the question
        subtopic (Optional[str]): The subtopic of the question
    """
    id: int
    exam_dtl_id: int
    question: str
    question_type: Optional[str] = None
    option1: Optional[str] = None
    option2: Optional[str] = None
    option3: Optional[str] = None
    option4: Optional[str] = None
    option5: Optional[str] = None
    answer: Optional[str] = None
    marks: Optional[float] = None
    negative_mark: Optional[float] = None
    topic: Optional[str] = None
    subtopic: Optional[str] = None


class ExamSolutionListResponse(BaseModel):
    """
    Pydantic model for a paginated list of exam solutions.

    Attributes:
        solutions (List[ExamSolutionResponse]): The list of solutions
        total (int): The total number of solutions
        page (int): The current page
        limit (int): The number of solutions per page
        pages (int): The total number of pages
        document (ExamDocumentResponse): The document the solutions belong to
    """
    solutions: List[ExamSolutionResponse]
    total: int
    page: int
    limit: int
    pages: int
    document: ExamDocumentResponse


class ExtractContentRequest(BaseModel):
    """
    Pydantic model for extracting content from a document.

    Attributes:
        document_id (int): The ID of the document to extract content from
    """
    document_id: int


class ExtractQuestionsRequest(BaseModel):
    """
    Pydantic model for extracting questions from a document.

    Attributes:
        document_id (int): The ID of the document to extract questions from
    """
    document_id: int


class GenerateSolutionsRequest(BaseModel):
    """
    Pydantic model for generating solutions for a document.

    Attributes:
        document_id (int): The ID of the document to generate solutions for
    """
    document_id: int
